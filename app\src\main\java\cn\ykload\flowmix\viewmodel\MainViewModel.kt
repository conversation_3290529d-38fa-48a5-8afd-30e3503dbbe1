package cn.ykload.flowmix.viewmodel

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import android.net.Uri
import android.util.Log
import androidx.activity.result.ActivityResultLauncher
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import cn.ykload.flowmix.audio.AudioEffectManager
import cn.ykload.flowmix.audio.SoundEffectManager
import cn.ykload.flowmix.data.AutoEqData
import cn.ykload.flowmix.file.FileAccessHelper
import cn.ykload.flowmix.file.FileValidationResult
import cn.ykload.flowmix.parser.AutoEqParser
import cn.ykload.flowmix.permission.PermissionManager
import cn.ykload.flowmix.utils.FileUtils
import cn.ykload.flowmix.utils.Constants
import cn.ykload.flowmix.viewmodel.DeviceConfigApplyCallback
import cn.ykload.flowmix.service.ServiceManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withContext
import cn.ykload.flowmix.data.EqBand
import java.io.InputStream

/**
 * 主界面ViewModel
 */
class MainViewModel(application: Application) : AndroidViewModel(application), DeviceConfigApplyCallback {

    companion object {
        private const val TAG = "MainViewModel"
        private const val PREFS_NAME = "flowmix_main_settings"
        private const val KEY_LONG_PRESS_TO_START_ENABLED = "long_press_to_start_enabled"
        private const val KEY_SOUND_EFFECT_ENABLED = "sound_effect_enabled"
        private const val KEY_FLOW_EQ_FREQUENCY_UPPER_LIMIT = "flow_eq_frequency_upper_limit"
    }

    private val audioEffectManager = AudioEffectManager()
    private val soundEffectManager = SoundEffectManager(application)
    private val autoEqParser = AutoEqParser()
    private val fileAccessHelper = FileAccessHelper(application)

    // SharedPreferences for settings persistence
    private val sharedPreferences: SharedPreferences =
        application.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    // 权限管理
    private var permissionManager: PermissionManager? = null
    private var permissionLauncher: ActivityResultLauncher<Array<String>>? = null
    private var manageStorageLauncher: ActivityResultLauncher<android.content.Intent>? = null

    // 服务管理
    private var serviceManager: ServiceManager? = null

    // UI状态
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()

    // FlowSync自动保存回调
    private var autoSaveCallback: cn.ykload.flowmix.viewmodel.DeviceConfigSaveCallback? = null

    // 标志：是否正在应用FlowSync配置（防止循环保存）
    private var isApplyingFlowSyncConfig = false

    // 标志：是否应该在应用AutoEq后自动保存（用于区分用户操作和系统操作）
    private var shouldAutoSaveAfterApply = true

    init {
        // 检查设备支持
        checkDeviceSupport()
        // 加载保存的设置
        loadSettings()
    }

    /**
     * 设置FlowSync自动保存回调
     */
    fun setAutoSaveCallback(callback: cn.ykload.flowmix.viewmodel.DeviceConfigSaveCallback) {
        autoSaveCallback = callback
    }

    /**
     * 立即保存当前配置到FlowSync
     * 用于在用户修改AutoEq配置时立即进行记忆
     */
    private fun saveCurrentConfigToFlowSync() {
        // 防止在应用FlowSync配置时触发循环保存
        if (isApplyingFlowSyncConfig) {
            Log.d("MainViewModel", "正在应用FlowSync配置，跳过自动保存")
            return
        }

        val currentData = _uiState.value.currentEqData
        if (currentData != null) {
            autoSaveCallback?.saveCurrentConfigToDevice(
                autoEqData = currentData,
                isLoudnessCompensationEnabled = _uiState.value.isLoudnessCompensationEnabled,
                globalGain = _uiState.value.globalGain
            )
        }
    }

    /**
     * 设置权限管理器
     */
    fun setPermissionManager(manager: PermissionManager) {
        permissionManager = manager
        checkPermissions()
    }

    /**
     * 设置权限启动器
     */
    fun setPermissionLauncher(launcher: ActivityResultLauncher<Array<String>>) {
        permissionLauncher = launcher
    }

    /**
     * 设置管理存储权限启动器
     */
    fun setManageStorageLauncher(launcher: ActivityResultLauncher<android.content.Intent>) {
        manageStorageLauncher = launcher
    }

    /**
     * 设置服务管理器
     */
    fun setServiceManager(manager: ServiceManager) {
        serviceManager = manager

        // 保活服务始终启用，如果有权限则自动启动服务
        if (_uiState.value.hasAllPermissions) {
            manager.startKeepAliveService(_uiState.value.isEffectEnabled)
        }
    }
    
    /**
     * 检查设备是否支持DynamicsProcessing
     */
    private fun checkDeviceSupport() {
        viewModelScope.launch {
            val isSupported = audioEffectManager.isDynamicsProcessingSupported()
            _uiState.value = _uiState.value.copy(
                isDeviceSupported = isSupported,
                errorMessage = if (!isSupported) Constants.ErrorMessages.DEVICE_NOT_SUPPORTED else null
            )
        }
    }

    /**
     * 检查权限状态
     */
    private fun checkPermissions() {
        permissionManager?.let { manager ->
            _uiState.value = _uiState.value.copy(
                hasAudioPermission = manager.hasAudioPermission(),
                hasBluetoothPermission = manager.hasBluetoothPermission(),
                hasStoragePermission = manager.hasStoragePermission()
            )
        }
    }

    /**
     * 请求权限
     */
    fun requestPermissions() {
        permissionManager?.let { manager ->
            val permissionsToRequest = manager.getPermissionsToRequest()

            if (permissionsToRequest.isNotEmpty()) {
                permissionLauncher?.launch(permissionsToRequest)
            }
        }
    }

    /**
     * 权限请求结果处理
     */
    fun onPermissionResult(permissions: Map<String, Boolean>) {
        checkPermissions()

        val deniedPermissions = permissions.filter { !it.value }.keys
        if (deniedPermissions.isNotEmpty()) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "部分权限被拒绝，可能影响应用功能"
            )
        }
    }

    /**
     * 管理存储权限结果处理
     */
    fun onManageStoragePermissionResult(granted: Boolean) {
        checkPermissions()
        if (!granted) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "存储权限被拒绝，无法读取文件"
            )
        }
    }
    
    /**
     * 导入AutoEq文件
     */
    fun importAutoEqFile(uri: Uri, inputStream: InputStream, fileName: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)

            try {
                // 验证文件
                val validationResult = fileAccessHelper.validateSelectedFile(uri)
                when (validationResult) {
                    is FileValidationResult.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            errorMessage = validationResult.message
                        )
                        return@launch
                    }
                    is FileValidationResult.Warning -> {
                        // 显示警告但继续处理
                        _uiState.value = _uiState.value.copy(
                            errorMessage = validationResult.message
                        )
                    }
                    FileValidationResult.Success -> {
                        // 清除之前的错误消息
                        _uiState.value = _uiState.value.copy(errorMessage = null)
                    }
                }

                // 解析文件
                val autoEqData = autoEqParser.parseFromInputStream(inputStream, fileName)

                if (autoEqData != null && autoEqData.isValid()) {
                    // 如果等响度补偿开启，需要同步更新整体增益滑动条的值
                    val newGlobalGain = if (_uiState.value.isLoudnessCompensationEnabled) {
                        autoEqData.calculateLoudnessCompensation()
                    } else {
                        _uiState.value.globalGain
                    }

                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        currentEqData = autoEqData,
                        selectedFileUri = uri,
                        selectedFileName = fileName,
                        globalGain = newGlobalGain
                    )
                    // 如果当前效果已启用，则重新应用新的AutoEq数据
                    if (_uiState.value.isEffectEnabled) {
                        applyAutoEq()
                    }

                    // 立即保存导入的AutoEq配置到FlowSync
                    saveCurrentConfigToFlowSync()
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = Constants.ErrorMessages.INVALID_FILE_FORMAT
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "${Constants.ErrorMessages.FILE_READ_ERROR}: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 应用AutoEq效果
     */
    fun applyAutoEq() {
        viewModelScope.launch {
            applyAutoEqInternal()
        }
    }
    
    /**
     * 切换效果开关
     */
    fun toggleEffect() {
        viewModelScope.launch {
            val newState = !_uiState.value.isEffectEnabled

            // 检查是否尝试启用效果但没有AutoEq配置
            if (newState && _uiState.value.currentEqData == null) {
                // 显示错误提示，告知用户需要先配置AutoEq
                _uiState.value = _uiState.value.copy(
                    errorMessage = "先配置AutoEq再启用Flowmix吧~"
                )
                return@launch
            }

            if (newState && _uiState.value.currentEqData != null) {
                // 如果要启用效果且有EQ数据，则应用AutoEq
                // 但不自动保存（因为这只是启用已有配置，不是修改配置）
                Log.d("MainViewModel", "toggleEffect: 启用效果，禁用自动保存")
                shouldAutoSaveAfterApply = false
                try {
                    applyAutoEqInternal()
                    // 播放启用音效
                    if (_uiState.value.isSoundEffectEnabled && _uiState.value.isEffectEnabled) {
                        soundEffectManager.playFlowmixOnSound()
                    }

                    // 更新服务状态
                    serviceManager?.updateFlowmixStatus(_uiState.value.isEffectEnabled)
                } finally {
                    shouldAutoSaveAfterApply = true // 确保恢复默认行为
                    Log.d("MainViewModel", "toggleEffect: 恢复自动保存标志")
                }
            } else {
                // 如果要禁用效果，则直接切换状态
                val success = audioEffectManager.setEnabled(newState)
                val finalState = if (success) newState else _uiState.value.isEffectEnabled
                _uiState.value = _uiState.value.copy(
                    isEffectEnabled = finalState,
                    errorMessage = if (!success) "切换效果状态失败" else null
                )

                // 播放相应的音效
                if (_uiState.value.isSoundEffectEnabled && success) {
                    if (finalState) {
                        soundEffectManager.playFlowmixOnSound()
                    } else {
                        soundEffectManager.playFlowmixOffSound()
                    }
                }

                // 更新服务状态
                serviceManager?.updateFlowmixStatus(finalState)
            }
        }
    }

    /**
     * 内部应用AutoEq方法（同步版本，用于控制自动保存时机）
     */
    private suspend fun applyAutoEqInternal() {
        val currentData = _uiState.value.currentEqData ?: return

        _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)

        try {
            // 根据等响度补偿设置处理EQ数据
            val processedData = if (_uiState.value.isLoudnessCompensationEnabled) {
                val compensation = currentData.calculateLoudnessCompensation()
                currentData.withLoudnessCompensation(compensation)
            } else {
                // 应用整体增益调节
                currentData.withGlobalGain(_uiState.value.globalGain)
            }

            // 当Flowmix开关开启时，优先使用热更新；否则强制完全重载
            val forceFullReload = !_uiState.value.isEffectEnabled
            Log.d("MainViewModel", "应用AutoEq配置: ${processedData.name}, 强制重载: $forceFullReload, 当前效果状态: ${_uiState.value.isEffectEnabled}")

            val success = audioEffectManager.applyAutoEq(processedData, forceFullReload)

            if (success) {
                Log.d("MainViewModel", "AutoEq配置应用成功: ${processedData.name}")
            } else {
                Log.e("MainViewModel", "AutoEq配置应用失败: ${processedData.name}")
            }

            _uiState.value = _uiState.value.copy(
                isLoading = false,
                isEffectEnabled = success,
                errorMessage = if (!success) Constants.ErrorMessages.AUDIO_EFFECT_APPLY_ERROR else null
            )

            // 更新服务状态
            if (success) {
                serviceManager?.updateFlowmixStatus(true)
            }

            // 自动保存配置到FlowSync（只有在应该自动保存时才保存）
            if (success && shouldAutoSaveAfterApply && !isApplyingFlowSyncConfig) {
                Log.d("MainViewModel", "自动保存配置到FlowSync (shouldAutoSave=$shouldAutoSaveAfterApply, isApplyingFlowSync=$isApplyingFlowSyncConfig)")
                autoSaveCallback?.saveCurrentConfigToDevice(
                    autoEqData = currentData,
                    isLoudnessCompensationEnabled = _uiState.value.isLoudnessCompensationEnabled,
                    globalGain = _uiState.value.globalGain
                )
            } else {
                Log.d("MainViewModel", "跳过自动保存 (success=$success, shouldAutoSave=$shouldAutoSaveAfterApply, isApplyingFlowSync=$isApplyingFlowSyncConfig)")
            }
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                errorMessage = "${Constants.ErrorMessages.AUDIO_EFFECT_APPLY_ERROR}: ${e.message}"
            )
        }
    }

    /**
     * 切换等响度补偿开关 - 显示确认对话框
     */
    fun toggleLoudnessCompensation() {
        _uiState.value = _uiState.value.copy(showLoudnessCompensationConfirmDialog = true)
    }

    /**
     * 确认切换等响度补偿
     */
    fun confirmToggleLoudnessCompensation() {
        viewModelScope.launch {
            val newState = !_uiState.value.isLoudnessCompensationEnabled

            // 如果开启等响度，需要将整体增益调节条设置为等响度补偿值
            // 如果关闭等响度，将整体增益调整回0
            val newGlobalGain = if (newState && _uiState.value.currentEqData != null) {
                _uiState.value.currentEqData!!.calculateLoudnessCompensation()
            } else {
                // 关闭等响度时，整体增益调整回0
                0f
            }

            _uiState.value = _uiState.value.copy(
                isLoudnessCompensationEnabled = newState,
                globalGain = newGlobalGain,
                showLoudnessCompensationConfirmDialog = false
            )

            // 如果当前有效果在运行，重新应用以使用新的补偿设置
            if (_uiState.value.isEffectEnabled && _uiState.value.currentEqData != null) {
                applyAutoEq()
            }

            // 立即保存配置变更到FlowSync
            saveCurrentConfigToFlowSync()
        }
    }

    /**
     * 取消等响度补偿确认对话框
     */
    fun dismissLoudnessCompensationConfirmDialog() {
        _uiState.value = _uiState.value.copy(showLoudnessCompensationConfirmDialog = false)
    }

    /**
     * 调节整体增益
     * @param gain 增益值（dB），范围 -15 到 +15
     */
    fun adjustGlobalGain(gain: Float) {
        viewModelScope.launch {
            val clampedGain = gain.coerceIn(-15f, 15f)

            // 如果调节了整体增益，自动关闭等响度补偿
            _uiState.value = _uiState.value.copy(
                globalGain = clampedGain,
                isLoudnessCompensationEnabled = false
            )

            // 如果当前有效果在运行，重新应用以使用新的增益设置
            if (_uiState.value.isEffectEnabled && _uiState.value.currentEqData != null) {
                applyAutoEq()
            }

            // 立即保存配置变更到FlowSync
            saveCurrentConfigToFlowSync()
        }
    }

    /**
     * 重置整体增益到0
     */
    fun resetGlobalGain() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                globalGain = 0f,
                isLoudnessCompensationEnabled = false
            )

            // 如果当前有效果在运行，重新应用
            if (_uiState.value.isEffectEnabled && _uiState.value.currentEqData != null) {
                applyAutoEq()
            }

            // 立即保存配置变更到FlowSync
            saveCurrentConfigToFlowSync()
        }
    }

    /**
     * 切换AutoEq Card展开状态
     */
    fun toggleAutoEqCardExpanded() {
        _uiState.value = _uiState.value.copy(
            isAutoEqCardExpanded = !_uiState.value.isAutoEqCardExpanded
        )
    }

    /**
     * 切换长按启动设置
     */
    fun toggleLongPressToStart() {
        val newValue = !_uiState.value.isLongPressToStartEnabled
        _uiState.value = _uiState.value.copy(
            isLongPressToStartEnabled = newValue
        )
        // 保存设置到SharedPreferences
        saveSettings()
    }

    /**
     * 切换原始频响曲线可见性
     */
    fun toggleOriginalCurveVisibility() {
        _uiState.value = _uiState.value.copy(
            isOriginalCurveVisible = !_uiState.value.isOriginalCurveVisible
        )
    }

    /**
     * 切换AutoEq调整后曲线可见性
     */
    fun toggleAutoEqCurveVisibility() {
        _uiState.value = _uiState.value.copy(
            isAutoEqCurveVisible = !_uiState.value.isAutoEqCurveVisible
        )
    }

    /**
     * 切换目标曲线可见性
     */
    fun toggleTargetCurveVisibility() {
        _uiState.value = _uiState.value.copy(
            isTargetCurveVisible = !_uiState.value.isTargetCurveVisible
        )
    }

    /**
     * 切换提示音设置
     */
    fun toggleSoundEffect() {
        val newValue = !_uiState.value.isSoundEffectEnabled
        _uiState.value = _uiState.value.copy(
            isSoundEffectEnabled = newValue
        )
        // 保存设置到SharedPreferences
        saveSettings()
    }

    /**
     * 设置FlowEq频段上限
     * @param upperLimit 频段上限 (Hz)
     */
    fun setFlowEqFrequencyUpperLimit(upperLimit: Float) {
        _uiState.value = _uiState.value.copy(
            flowEqFrequencyUpperLimit = upperLimit
        )
        // 保存设置到SharedPreferences
        saveSettings()
    }

    /**
     * 调节EQ频段增益
     * @param frequency 频率
     * @param deltaGain 增益变化量（dB）
     */
    fun adjustEqBand(frequency: Float, deltaGain: Float) {
        var currentData = _uiState.value.currentEqData

        // 如果没有EQ数据，创建一个新的FlowEq
        if (currentData == null) {
            currentData = createDefaultFlowEq()

            // 如果等响度补偿开启，需要同步更新整体增益滑动条的值
            val newGlobalGain = if (_uiState.value.isLoudnessCompensationEnabled) {
                currentData.calculateLoudnessCompensation()
            } else {
                _uiState.value.globalGain
            }

            // 生成新的FlowEq名称
            val flowEqName = "FlowEq_${System.currentTimeMillis()}"
            val updatedData = currentData.copy(name = flowEqName)

            _uiState.value = _uiState.value.copy(
                currentEqData = updatedData,
                selectedFileName = "${flowEqName}.txt",
                globalGain = newGlobalGain
            )
        }

        // 查找并更新对应频段
        val updatedBands = currentData.bands.map { band ->
            if (band.frequency == frequency) {
                val newGain = (band.gain + deltaGain).coerceIn(-30f, 30f)
                band.copy(gain = newGain)
            } else {
                band
            }
        }

        val updatedEqData = currentData.copy(bands = updatedBands)

        // 如果等响度补偿开启，需要同步更新整体增益滑动条的值
        val newGlobalGain = if (_uiState.value.isLoudnessCompensationEnabled) {
            updatedEqData.calculateLoudnessCompensation()
        } else {
            _uiState.value.globalGain
        }

        _uiState.value = _uiState.value.copy(
            currentEqData = updatedEqData,
            globalGain = newGlobalGain
        )

        // 如果当前效果已启用，则重新应用更新后的EQ数据
        if (_uiState.value.isEffectEnabled) {
            applyAutoEq()
        }

        // 立即保存配置变更到FlowSync
        saveCurrentConfigToFlowSync()
    }

    /**
     * 设置EQ频段的绝对增益值
     * @param frequency 频率
     * @param newGain 新的增益值（dB）
     */
    fun setBandGain(frequency: Float, newGain: Float) {
        var currentData = _uiState.value.currentEqData

        // 如果没有EQ数据，创建一个新的FlowEq
        if (currentData == null) {
            currentData = createDefaultFlowEq()

            // 如果等响度补偿开启，需要同步更新整体增益滑动条的值
            val newGlobalGain = if (_uiState.value.isLoudnessCompensationEnabled) {
                currentData.calculateLoudnessCompensation()
            } else {
                _uiState.value.globalGain
            }

            // 生成新的FlowEq名称
            val flowEqName = "FlowEq_${System.currentTimeMillis()}"
            val updatedData = currentData.copy(name = flowEqName)

            _uiState.value = _uiState.value.copy(
                currentEqData = updatedData,
                selectedFileName = "${flowEqName}.txt",
                globalGain = newGlobalGain
            )
        }

        // 查找并更新对应频段
        val updatedBands = currentData.bands.map { band ->
            if (band.frequency == frequency) {
                val clampedGain = newGain.coerceIn(-30f, 30f)
                band.copy(gain = clampedGain)
            } else {
                band
            }
        }

        val updatedEqData = currentData.copy(bands = updatedBands)

        // 如果等响度补偿开启，需要同步更新整体增益滑动条的值
        val newGlobalGain = if (_uiState.value.isLoudnessCompensationEnabled) {
            updatedEqData.calculateLoudnessCompensation()
        } else {
            _uiState.value.globalGain
        }

        _uiState.value = _uiState.value.copy(
            currentEqData = updatedEqData,
            globalGain = newGlobalGain
        )

        // 如果当前效果已启用，则重新应用更新后的EQ数据
        if (_uiState.value.isEffectEnabled) {
            applyAutoEq()
        }

        // 立即保存配置变更到FlowSync
        saveCurrentConfigToFlowSync()
    }

    /**
     * 更新可见EQ频段范围
     */
    fun updateVisibleEqBandRange(range: IntRange?) {
        _uiState.value = _uiState.value.copy(visibleEqBandRange = range)
    }

    /**
     * 创建默认的FlowEq数据（127个频段，增益都为0）
     */
    private fun createDefaultFlowEq(): AutoEqData {
        val defaultBands = Constants.AutoEqFormat.STANDARD_FREQUENCIES.map { frequency ->
            EqBand(frequency = frequency, gain = 0f)
        }
        return AutoEqData(bands = defaultBands, name = "FlowEq")
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    /**
     * 显示导出对话框
     */
    fun showExportDialog() {
        val currentData = _uiState.value.currentEqData
        if (currentData != null) {
            val defaultName = _uiState.value.selectedFileName?.let { fileName ->
                // 移除扩展名
                if (fileName.endsWith(".txt", ignoreCase = true)) {
                    fileName.substring(0, fileName.length - 4)
                } else {
                    fileName
                }
            } ?: currentData.name

            _uiState.value = _uiState.value.copy(
                showExportDialog = true,
                exportFileName = defaultName
            )
        }
    }

    /**
     * 隐藏导出对话框
     */
    fun hideExportDialog() {
        _uiState.value = _uiState.value.copy(
            showExportDialog = false,
            exportFileName = ""
        )
    }

    /**
     * 更新导出文件名
     */
    fun updateExportFileName(fileName: String) {
        _uiState.value = _uiState.value.copy(exportFileName = fileName)
    }

    /**
     * 导出AutoEq文件
     */
    fun exportAutoEqFile() {
        val currentData = _uiState.value.currentEqData
        val fileName = _uiState.value.exportFileName.trim()

        if (currentData == null) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "没有可导出的EQ数据"
            )
            return
        }

        if (fileName.isEmpty()) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "请输入文件名"
            )
            return
        }

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isExporting = true)

            try {
                // 获取要导出的数据（考虑当前的增益调节）
                val exportData = if (_uiState.value.isLoudnessCompensationEnabled) {
                    val compensation = currentData.calculateLoudnessCompensation()
                    currentData.withLoudnessCompensation(compensation)
                } else if (_uiState.value.globalGain != 0f) {
                    currentData.withGlobalGain(_uiState.value.globalGain)
                } else {
                    currentData
                }

                // 生成AutoEq格式的文件内容
                val fileContent = generateAutoEqFileContent(exportData, fileName)

                // 保存文件到指定目录
                val success = saveAutoEqFile(fileName, fileContent)

                if (success) {
                    _uiState.value = _uiState.value.copy(
                        isExporting = false,
                        showExportDialog = false,
                        exportFileName = "",
                        successMessage = "文件已导出到 Documents/Flowmix/AutoEq/$fileName.txt"
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isExporting = false,
                        errorMessage = "导出失败，请检查存储权限"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isExporting = false,
                    errorMessage = "导出失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 清除成功消息
     */
    fun clearSuccess() {
        _uiState.value = _uiState.value.copy(successMessage = null)
    }

    /**
     * 生成AutoEq格式的文件内容
     */
    private fun generateAutoEqFileContent(eqData: AutoEqData, fileName: String): String {
        // 生成GraphicEQ格式，不包含任何注释
        val eqPairs = eqData.bands.map { band ->
            "${band.frequency.toInt()} ${"%.1f".format(band.gain)}"
        }

        return "GraphicEQ: ${eqPairs.joinToString("; ")}"
    }

    /**
     * 保存AutoEq文件到指定目录
     */
    private suspend fun saveAutoEqFile(fileName: String, content: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 使用Android规范的公共文档目录
                val documentsDir = android.os.Environment.getExternalStoragePublicDirectory(
                    android.os.Environment.DIRECTORY_DOCUMENTS
                )
                val flowmixDir = java.io.File(documentsDir, "Flowmix")
                val autoEqDir = java.io.File(flowmixDir, "AutoEq")

                // 创建目录（如果不存在）
                if (!autoEqDir.exists()) {
                    autoEqDir.mkdirs()
                }

                // 确保文件名以.txt结尾
                val finalFileName = if (fileName.endsWith(".txt", ignoreCase = true)) {
                    fileName
                } else {
                    "$fileName.txt"
                }

                // 创建文件
                val file = java.io.File(autoEqDir, finalFileName)
                file.writeText(content)

                true
            } catch (e: Exception) {
                Log.e("MainViewModel", "保存文件失败", e)
                false
            }
        }
    }

    /**
     * 处理文件读取错误
     */
    fun onFileReadError(message: String) {
        _uiState.value = _uiState.value.copy(
            isLoading = false,
            errorMessage = message
        )
    }
    
    /**
     * FlowEq - 一键拟合目标曲线 - 显示确认对话框
     */
    fun performFlowEq(
        originalFrequencyData: cn.ykload.flowmix.data.MeasurementCondition,
        targetCurveData: cn.ykload.flowmix.data.MeasurementCondition
    ) {
        _uiState.value = _uiState.value.copy(
            showFlowEqConfirmDialog = true,
            pendingFlowEqData = Pair(originalFrequencyData, targetCurveData)
        )
    }

    /**
     * 确认执行FlowEq拟合
     */
    fun confirmPerformFlowEq() {
        val pendingData = _uiState.value.pendingFlowEqData
        if (pendingData == null) {
            _uiState.value = _uiState.value.copy(showFlowEqConfirmDialog = false)
            return
        }

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isFlowEqProcessing = true,
                showFlowEqConfirmDialog = false,
                pendingFlowEqData = null
            )

            try {
                // 计算FlowEq
                val flowEqData = calculateFlowEq(pendingData.first, pendingData.second)

                // 如果等响度补偿开启，需要同步更新整体增益滑动条的值
                val newGlobalGain = if (_uiState.value.isLoudnessCompensationEnabled) {
                    flowEqData.calculateLoudnessCompensation()
                } else {
                    _uiState.value.globalGain
                }

                // 更新EQ数据
                _uiState.value = _uiState.value.copy(
                    currentEqData = flowEqData,
                    selectedFileName = "${flowEqData.name}.txt",
                    globalGain = newGlobalGain,
                    isFlowEqProcessing = false
                )
                // 如果当前效果已启用，则重新应用新生成的FlowEq数据
                if (_uiState.value.isEffectEnabled) {
                    applyAutoEq()
                }

                // 立即保存新生成的FlowEq配置到FlowSync
                saveCurrentConfigToFlowSync()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isFlowEqProcessing = false,
                    errorMessage = "FlowEq拟合失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 取消FlowEq确认对话框
     */
    fun dismissFlowEqConfirmDialog() {
        _uiState.value = _uiState.value.copy(
            showFlowEqConfirmDialog = false,
            pendingFlowEqData = null
        )
    }

    /**
     * 计算FlowEq配置
     */
    private suspend fun calculateFlowEq(
        originalData: cn.ykload.flowmix.data.MeasurementCondition,
        targetData: cn.ykload.flowmix.data.MeasurementCondition
    ): AutoEqData {
        return withContext(Dispatchers.Default) {
            val originalFreqs = originalData.getValidFrequencies()
            val originalSpls = originalData.getValidSplValues()
            val targetFreqs = targetData.getValidFrequencies()
            val targetSpls = targetData.getValidSplValues()

            if (originalFreqs.isEmpty() || targetFreqs.isEmpty()) {
                throw IllegalArgumentException("频响数据无效")
            }

            // 使用标准EQ频段，根据用户设置的上限进行过滤
            val frequencyUpperLimit = _uiState.value.flowEqFrequencyUpperLimit
            val standardFreqs = if (frequencyUpperLimit >= 24000f) {
                // 使用完整频段
                Constants.AutoEqFormat.STANDARD_FREQUENCIES
            } else {
                // 过滤掉超过上限的频段
                Constants.AutoEqFormat.STANDARD_FREQUENCIES.filter { it <= frequencyUpperLimit }.toTypedArray()
            }

            // 第一步：计算所有频段的理想增益
            val idealGains = mutableListOf<Float>()
            for (freq in standardFreqs) {
                // 在原始频响中插值获取SPL值
                val originalSpl = interpolateSpl(freq, originalFreqs, originalSpls)
                // 在目标曲线中插值获取SPL值
                val targetSpl = interpolateSpl(freq, targetFreqs, targetSpls)

                // 计算需要的增益（目标 - 原始）
                val requiredGain = targetSpl - originalSpl
                idealGains.add(requiredGain)
            }

            // 第二步：找到最大的正增益，用于整体偏移
            val maxPositiveGain = idealGains.maxOrNull() ?: 0f
            val globalOffset = if (maxPositiveGain > 0f) maxPositiveGain else 0f

            // 第三步：应用全局偏移，确保所有增益都为负值或零
            val eqBands = mutableListOf<EqBand>()
            for (i in standardFreqs.indices) {
                val freq = standardFreqs[i]
                val adjustedGain = idealGains[i] - globalOffset

                // 确保增益在合理范围内（-30到0）
                val finalGain = adjustedGain.coerceIn(-30f, 0f)

                eqBands.add(EqBand(frequency = freq, gain = finalGain))
            }

            // 应用平滑处理，避免过于剧烈的增益变化
            val smoothedBands = smoothEqCurve(eqBands)

            // 生成带时间戳的FlowEq名称，确保与UI显示一致
            val flowEqName = "FlowEq_${System.currentTimeMillis()}"
            AutoEqData(bands = smoothedBands, name = flowEqName)
        }
    }

    /**
     * 在频响数据中插值获取指定频率的SPL值
     */
    private fun interpolateSpl(
        targetFreq: Float,
        frequencies: List<Float>,
        splValues: List<Float>
    ): Float {
        if (frequencies.isEmpty() || splValues.isEmpty()) return 0f
        if (frequencies.size != splValues.size) return 0f

        // 如果目标频率超出范围，返回边界值
        if (targetFreq <= frequencies.first()) return splValues.first()
        if (targetFreq >= frequencies.last()) return splValues.last()

        // 找到目标频率的位置
        for (i in 0 until frequencies.size - 1) {
            val freq1 = frequencies[i]
            val freq2 = frequencies[i + 1]

            if (targetFreq >= freq1 && targetFreq <= freq2) {
                val spl1 = splValues[i]
                val spl2 = splValues[i + 1]

                // 对数插值（频率是对数刻度）
                val logFreq1 = kotlin.math.ln(freq1)
                val logFreq2 = kotlin.math.ln(freq2)
                val logTargetFreq = kotlin.math.ln(targetFreq)

                val ratio = (logTargetFreq - logFreq1) / (logFreq2 - logFreq1)
                return spl1 + ratio * (spl2 - spl1)
            }
        }

        return 0f
    }

    /**
     * 平滑EQ曲线，避免过于剧烈的变化
     */
    private fun smoothEqCurve(bands: List<EqBand>): List<EqBand> {
        if (bands.size < 3) return bands

        val smoothedBands = mutableListOf<EqBand>()

        // 第一个频段保持不变
        smoothedBands.add(bands[0])

        // 中间频段使用3点平滑
        for (i in 1 until bands.size - 1) {
            val prevGain = bands[i - 1].gain
            val currentGain = bands[i].gain
            val nextGain = bands[i + 1].gain

            // 简单的3点平均平滑
            val smoothedGain = (prevGain + currentGain * 2 + nextGain) / 4f

            smoothedBands.add(
                EqBand(
                    frequency = bands[i].frequency,
                    gain = smoothedGain.coerceIn(-30f, 0f)
                )
            )
        }

        // 最后一个频段保持不变
        smoothedBands.add(bands.last())

        return smoothedBands
    }

    /**
     * 重置状态
     */
    fun reset() {
        viewModelScope.launch {
            audioEffectManager.releaseEffect()
            _uiState.value = MainUiState(
                isDeviceSupported = _uiState.value.isDeviceSupported
            )
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        audioEffectManager.releaseEffect()
        soundEffectManager.cleanup()
    }

    // DeviceConfigApplyCallback 接口实现
    override fun applyAutoEqConfig(
        autoEqData: AutoEqData,
        isLoudnessCompensationEnabled: Boolean,
        globalGain: Float
    ) {
        viewModelScope.launch {
            try {
                Log.d("MainViewModel", "应用FlowSync AutoEq配置: ${autoEqData.name}")

                // 设置标志，防止循环保存
                isApplyingFlowSyncConfig = true
                shouldAutoSaveAfterApply = false

                // 检查配置是否有变化
                val currentState = _uiState.value
                val configChanged = currentState.currentEqData?.name != autoEqData.name ||
                        currentState.isLoudnessCompensationEnabled != isLoudnessCompensationEnabled ||
                        currentState.globalGain != globalGain ||
                        !autoEqData.isEquivalentTo(currentState.currentEqData)

                // 更新UI状态
                _uiState.value = _uiState.value.copy(
                    currentEqData = autoEqData,
                    isLoudnessCompensationEnabled = isLoudnessCompensationEnabled,
                    globalGain = globalGain,
                    selectedFileName = autoEqData.name
                )

                // 只有在配置有变化且当前效果已启用时，才重新应用AutoEq数据
                if (_uiState.value.isEffectEnabled && configChanged) {
                    Log.d("MainViewModel", "AutoEq配置有变化，重新应用")
                    applyAutoEqInternal()
                } else if (_uiState.value.isEffectEnabled) {
                    Log.d("MainViewModel", "AutoEq配置无变化，跳过重新应用")
                } else {
                    Log.d("MainViewModel", "效果未启用，跳过应用AutoEq")
                }
            } catch (e: Exception) {
                Log.e("MainViewModel", "应用FlowSync AutoEq配置失败", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = "应用AutoEq配置失败: ${e.message}"
                )
            } finally {
                // 重置标志
                isApplyingFlowSyncConfig = false
                shouldAutoSaveAfterApply = true
                Log.d("MainViewModel", "FlowSync AutoEq配置应用完成，恢复自动保存")
            }
        }
    }

    // FrequencyResponseViewModel引用，用于应用频响配置
    private var frequencyResponseViewModel: cn.ykload.flowmix.viewmodel.FrequencyResponseViewModel? = null

    /**
     * 设置FrequencyResponseViewModel引用
     */
    fun setFrequencyResponseViewModel(viewModel: cn.ykload.flowmix.viewmodel.FrequencyResponseViewModel) {
        frequencyResponseViewModel = viewModel
    }

    override fun applyFrequencyResponseConfig(
        dataSource: String?,
        brand: String?,
        headphone: String?,
        measurementCondition: String?
    ) {
        Log.d("MainViewModel", "应用FlowSync频响配置: $dataSource - $brand $headphone")

        // 通过FrequencyResponseViewModel应用配置（目标曲线已从同步范围中移除）
        frequencyResponseViewModel?.applyFlowSyncConfig(
            dataSource = dataSource,
            brand = brand,
            headphone = headphone,
            measurementCondition = measurementCondition
        )
    }

    override fun resetAllConfigs() {
        viewModelScope.launch {
            try {
                Log.d("MainViewModel", "重置所有配置（检测到新设备）")

                // 停止当前音频效果
                audioEffectManager.releaseEffect()

                // 重置AutoEq相关状态
                _uiState.value = _uiState.value.copy(
                    currentEqData = null,
                    selectedFileUri = null,
                    selectedFileName = null,
                    isEffectEnabled = false,
                    isLoudnessCompensationEnabled = false,
                    globalGain = 0f,
                    isAutoEqCardExpanded = false,
                    visibleEqBandRange = null
                )

                // 重置频响配置
                frequencyResponseViewModel?.resetAllConfigs()

                Log.d("MainViewModel", "所有配置已重置")
            } catch (e: Exception) {
                Log.e("MainViewModel", "重置配置失败", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = "重置配置失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 从SharedPreferences加载设置
     */
    private fun loadSettings() {
        try {
            val isLongPressToStartEnabled = sharedPreferences.getBoolean(KEY_LONG_PRESS_TO_START_ENABLED, true)
            val isSoundEffectEnabled = sharedPreferences.getBoolean(KEY_SOUND_EFFECT_ENABLED, false)
            val flowEqFrequencyUpperLimit = sharedPreferences.getFloat(KEY_FLOW_EQ_FREQUENCY_UPPER_LIMIT, 15000f)

            _uiState.value = _uiState.value.copy(
                isLongPressToStartEnabled = isLongPressToStartEnabled,
                isSoundEffectEnabled = isSoundEffectEnabled,
                flowEqFrequencyUpperLimit = flowEqFrequencyUpperLimit
            )

            Log.d(TAG, "已加载设置: 长按启动=$isLongPressToStartEnabled, 提示音=$isSoundEffectEnabled, FlowEq频段上限=${flowEqFrequencyUpperLimit}Hz")
        } catch (e: Exception) {
            Log.e(TAG, "加载设置失败", e)
        }
    }

    /**
     * 保存设置到SharedPreferences
     */
    private fun saveSettings() {
        try {
            val currentState = _uiState.value
            sharedPreferences.edit()
                .putBoolean(KEY_LONG_PRESS_TO_START_ENABLED, currentState.isLongPressToStartEnabled)
                .putBoolean(KEY_SOUND_EFFECT_ENABLED, currentState.isSoundEffectEnabled)
                .putFloat(KEY_FLOW_EQ_FREQUENCY_UPPER_LIMIT, currentState.flowEqFrequencyUpperLimit)
                .apply()

            Log.d(TAG, "已保存设置: 长按启动=${currentState.isLongPressToStartEnabled}, 提示音=${currentState.isSoundEffectEnabled}, FlowEq频段上限=${currentState.flowEqFrequencyUpperLimit}Hz")
        } catch (e: Exception) {
            Log.e(TAG, "保存设置失败", e)
        }
    }
}

/**
 * 主界面UI状态
 */
data class MainUiState(
    val isLoading: Boolean = false,
    val isDeviceSupported: Boolean = true,
    val currentEqData: AutoEqData? = null,
    val selectedFileUri: Uri? = null,
    val selectedFileName: String? = null,
    val isEffectEnabled: Boolean = false,
    val isLoudnessCompensationEnabled: Boolean = false,
    val globalGain: Float = 0f, // 整体增益调节，范围 -15dB 到 +15dB
    val errorMessage: String? = null,
    val successMessage: String? = null,
    val hasAudioPermission: Boolean = false,
    val hasBluetoothPermission: Boolean = false,
    val hasStoragePermission: Boolean = false,
    val isFlowEqProcessing: Boolean = false,
    val isAutoEqCardExpanded: Boolean = false,
    val visibleEqBandRange: IntRange? = null,
    val showExportDialog: Boolean = false,
    val exportFileName: String = "",
    val flowEqFrequencyUpperLimit: Float = 15000f, // FlowEq拟合频段上限，默认15kHz
    val isExporting: Boolean = false,
    // 频响图表曲线可见性状态
    val isOriginalCurveVisible: Boolean = true,
    val isAutoEqCurveVisible: Boolean = true,
    val isTargetCurveVisible: Boolean = true,
    // 设置项
    val isLongPressToStartEnabled: Boolean = true, // 长按启动 Flowmix
    val isSoundEffectEnabled: Boolean = false, // Flowmix 提示音
    // 确认对话框状态
    val showFlowEqConfirmDialog: Boolean = false,
    val showLoudnessCompensationConfirmDialog: Boolean = false,
    val pendingFlowEqData: Pair<cn.ykload.flowmix.data.MeasurementCondition, cn.ykload.flowmix.data.MeasurementCondition>? = null
) {
    val hasEqData: Boolean get() = currentEqData != null
    val canApplyEffect: Boolean get() = hasEqData && isDeviceSupported && !isLoading && hasAudioPermission
    val canSelectFile: Boolean get() = hasStoragePermission
    val hasAllPermissions: Boolean get() = hasAudioPermission && hasBluetoothPermission && hasStoragePermission
}
